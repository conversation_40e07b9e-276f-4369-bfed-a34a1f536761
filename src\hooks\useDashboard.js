import { useState, useEffect, useCallback, useRef } from 'react';
import apiService from '../services/apiService';

/**
 * Custom hook for managing dashboard data
 * Handles fetching overview stats and results data
 */
export const useDashboard = (currentPage = 1, limit = 10) => {
  // Use ref to track if component is mounted to prevent state updates after unmount
  const isMountedRef = useRef(true);
  // Add ref to track if data is currently being fetched to prevent duplicate calls
  const isFetchingRef = useRef(false);

  const [data, setData] = useState({
    results: [], // Keep for backward compatibility, but will contain jobs data
    jobs: [], // New jobs data
    overview: {
      archiveStats: {
        completed: 0,
        failed: 0,
        processing: 0
      },
      tokenBalance: {
        tokenBalance: 0
      },
      // Keep legacy fields for backward compatibility
      summary: {
        total_assessments: 0,
        this_month: 0,
        success_rate: 0
      },
      recent_results: [],
      archetype_summary: {
        most_common: '',
        frequency: 0,
        last_archetype: '',
        unique_archetypes: 0,
        archetype_trend: ''
      }
    },
    pagination: {
      page: 1,
      limit: 10,
      total: 0,
      totalPages: 0
    }
  });

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  const fetchDashboardData = useCallback(async () => {
    // Prevent API calls if component is unmounted (helps with StrictMode double calls)
    if (!isMountedRef.current) {
      console.log('⚠️ Component unmounted, skipping API call');
      return;
    }

    // Prevent duplicate API calls
    if (isFetchingRef.current) {
      console.log('⚠️ Already fetching data, skipping duplicate call');
      return;
    }

    isFetchingRef.current = true;
    console.log('🚀 Starting fetchDashboardData...', { currentPage, limit, mounted: isMountedRef.current });

    try {
      setLoading(true);
      setError('');

      console.log('📡 Making API calls...');

      // Fetch archive stats, token balance, and results data in parallel
      // Use getResults instead of getUserJobs since /api/archive/jobs returns 403
      const [archiveStatsResponse, tokenBalanceResponse, resultsResponse] = await Promise.all([
        apiService.getStats(),
        apiService.getTokenBalance(),
        apiService.getResults({ page: currentPage, limit })
      ]);

      console.log('✅ All API calls completed');

      // Debug logging to understand response structure
      console.log('🔍 Dashboard API Responses:', {
        archiveStats: archiveStatsResponse,
        tokenBalance: tokenBalanceResponse,
        results: resultsResponse
      });

      // Debug logging for extracted data
      console.log('📊 Extracted Data:', {
        archiveStatsRaw: archiveStatsResponse.data,
        tokenBalanceRaw: tokenBalanceResponse.data,
        resultsRaw: resultsResponse.data
      });

      // Check again if component is still mounted before updating state
      if (!isMountedRef.current) return;

      const newData = { ...data };

      // Update overview with new data structure
      // Handle different response structures for each endpoint
      let archiveStats = { completed: 0, failed: 0, processing: 0 };
      let tokenBalance = { tokenBalance: 0 };

      // Archive Stats: API returns {success: true, data: {...}}
      if (archiveStatsResponse && archiveStatsResponse.data) {
        archiveStats = {
          completed: archiveStatsResponse.data.completed || 0,
          failed: archiveStatsResponse.data.failed || 0,
          processing: archiveStatsResponse.data.processing || 0,
          total_analyses: archiveStatsResponse.data.total_analyses || 0,
          latest_analysis: archiveStatsResponse.data.latest_analysis || null,
          most_common_archetype: archiveStatsResponse.data.most_common_archetype || ''
        };
      }

      // Token Balance: API returns {success: true, data: {user_id: '...', token_balance: 0}}
      if (tokenBalanceResponse && tokenBalanceResponse.data) {
        tokenBalance = {
          tokenBalance: tokenBalanceResponse.data.token_balance || 0,
          user_id: tokenBalanceResponse.data.user_id
        };
      }

      newData.overview = {
        ...newData.overview,
        archiveStats,
        tokenBalance
      };

      // Handle results response - API returns {success: true, data: {results: [...], pagination: {...}}}
      if (resultsResponse && resultsResponse.data) {
        const resultsData = resultsResponse.data.results || [];
        const paginationData = resultsResponse.data.pagination || {};

        newData.jobs = resultsData; // Keep jobs for compatibility
        newData.results = resultsData; // Keep results for backward compatibility

        // Calculate pagination from results response
        const total = paginationData.total || 0;
        const totalPages = Math.ceil(total / limit);

        newData.pagination = {
          page: currentPage,
          limit,
          total,
          totalPages,
          hasNext: paginationData.hasNext || false,
          hasPrev: paginationData.hasPrev || false
        };
      } else {
        // If results API fails, set empty data
        newData.jobs = [];
        newData.results = [];
        newData.pagination = {
          page: currentPage,
          limit,
          total: 0,
          totalPages: 0
        };
      }

      console.log('💾 Setting new data:', newData);
      console.log('🔍 Final processed data structure:', {
        archiveStats,
        tokenBalance,
        resultsCount: newData.results.length,
        paginationTotal: newData.pagination.total
      });
      setData(newData);

    } catch (err) {
      console.error('❌ Error in fetchDashboardData:', err);
      // Only update error state if component is still mounted
      if (isMountedRef.current) {
        setError(err.response?.data?.message || 'Failed to load dashboard data');
      }
    } finally {
      // Reset fetching flag and loading state
      isFetchingRef.current = false;
      // Only update loading state if component is still mounted
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  }, [currentPage, limit]);

  const deleteResult = useCallback(async (jobIdOrResultId) => {
    try {
      // For now, we'll use the existing deleteResult API
      // In the future, this might need to be updated to handle job deletion specifically
      const response = await apiService.deleteResult(jobIdOrResultId);
      if (response.success) {
        // Remove from local state (both results and jobs arrays)
        setData(prevData => ({
          ...prevData,
          results: prevData.results.filter(item =>
            item.id !== jobIdOrResultId &&
            item.job_id !== jobIdOrResultId &&
            item.result_id !== jobIdOrResultId
          ),
          jobs: prevData.jobs.filter(job =>
            job.job_id !== jobIdOrResultId &&
            job.result_id !== jobIdOrResultId
          )
        }));

        // Refresh data to get updated stats
        await fetchDashboardData();
        return { success: true };
      }
    } catch (err) {
      const errorMessage = err.response?.data?.message || 'Failed to delete item';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  }, []);

  const refreshData = useCallback(() => {
    fetchDashboardData();
  }, []);

  useEffect(() => {
    fetchDashboardData();

    // Cleanup function to mark component as unmounted
    return () => {
      isMountedRef.current = false;
      isFetchingRef.current = false;
    };
  }, [currentPage, limit]); // Only depend on actual data dependencies, not the callback

  return {
    data,
    loading,
    error,
    deleteResult,
    refreshData,
    setError
  };
};

export default useDashboard;
